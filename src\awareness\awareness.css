:root {
 --primary-font: "Raleway";
 --secondary-font: "Playfair Display";
 --primary-color: #292b33;
 --secondary-color: #8d8cc4;
 --accent-color: #d8bb93;
 --text-color: #292b33;
 --background-color: #ffffff;
}

* {
 margin: 0;
 padding: 0;
 box-sizing: border-box;
}

body {
 color: var(--text-color);
 line-height: 1.6;
 background-color: var(--background-color);
}

/* Ensure all paragraphs use Raleway font */
p {
 font-family: "Raleway", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
  sans-serif !important;
 font-weight: 400 !important;
}

/* Ensure strong elements use Raleway font with proper weight */
strong {
 font-weight: 700 !important;
}

.container {
 max-width: 1440px;
 margin: 0 auto;
 padding: 0 2rem;
}

.section {
 padding: 4rem 0;
 position: relative;
 opacity: 0;
 transform: translateY(20px);
 transition: opacity 0.6s ease, transform 0.6s ease;
}

.section.visible {
 opacity: 1;
 transform: translateY(0);
}

.section-content {
 max-width: 1140px;
 margin: 0 auto;
}

.section-header {
 margin-bottom: 3rem;
}

.title {
 font-family: var(--secondary-font);
 color: var(--primary-color);
 display: flex;
 align-items: baseline;
}

.title .number {
 font-style: italic;
 font-size: 55px;
}

.title .text {
 font-size: 140px;
 line-height: 154px;
 margin-left: 1rem;
}

.description {
 font-size: 20px;
 line-height: 26px;
 max-width: 753px;
 margin-top: 1.5rem;
}

.section-body {
 font-family: "Raleway", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
  sans-serif !important;
 font-size: 20px;
 line-height: 26px;
}

.section-body p {
 font-family: "Raleway", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
  sans-serif !important;
 font-weight: 400 !important;
 margin-bottom: 1.5rem;
}

.section-body p strong {
 font-family: "Raleway", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
  sans-serif !important;
 font-weight: 700 !important;
}

.section-subtitle {
 text-align: center;
 margin: 2rem 0;
}

.section-subtitle h3 {
 font-size: 35px;
 line-height: 35px;
}

.italic-text {
 font-family: var(--secondary-font);
 font-style: italic;
 font-weight: 700;
}

/* Card styles */
.card {
 background: white;
 border-radius: 8px;
 padding: 2rem;
 box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
 transition: transform 0.3s ease;
}

.card:hover {
 transform: translateY(-5px);
}

.card.expanded {
 transform: scale(1.02);
}

/* Image styles */
.feature-image {
 width: 100%;
 height: auto;
 border-radius: 8px;
}

.image-grid {
 display: grid;
 grid-template-columns: repeat(auto-fit, minmax(270px, 1fr));
 gap: 1.25rem;
 margin-top: 2rem;
}

.image-grid img {
 width: 100%;
 height: 283px;
 object-fit: cover;
 border-radius: 8px;
}

.image-split {
 display: flex;
 gap: 2rem;
 margin: 2rem 0;
}

.split-image {
 width: 50%;
 height: 900px;
 object-fit: cover;
}

.full-width-image {
 width: 100%;
 height: auto;
}

/* Decorative elements */
.decorative-element {
 position: absolute;
 width: 136px;
 height: 277px;
 top: 103px;
 right: 0;
 background-color: var(--accent-color);
}

/* Star badge */
.star-badge {
 position: absolute;
 width: 209px;
 height: 205px;
 transform: rotate(8.13deg);
 box-shadow: 2px 4px 4px rgba(0, 0, 0, 0.25);
 background: var(--secondary-color);
 border-radius: 50%;
 display: flex;
 align-items: center;
 justify-content: center;
 color: white;
}

.star-content {
 text-align: center;
 transform: rotate(-8.13deg);
}

.star-content .percentage {
 font-size: 55px;
 font-weight: bold;
 display: block;
}

.star-content .text {
 font-size: 16px;
}

/* Dark banner */
.dark-banner {
 background-color: var(--primary-color);
 color: white;
 padding: 2rem;
 margin: 2rem 0;
 text-align: center;
}

.dark-banner h3 {
 font-size: 65px;
}

/* Text utilities */
.text-center {
 text-align: center;
}

.large-text {
 font-size: 35px;
 line-height: 1.2;
 margin: 2rem 0;
}

.section-intro {
 font-size: 26px;
 line-height: 32px;
 margin: 2rem 0;
 color: var(--text-color);
}

.key-message {
 text-align: center;
 margin: 3rem 0;
 font-size: 35px;
 line-height: 1.2;
}

.highlight-section {
 background-color: var(--secondary-color);
 color: white;
 padding: 4rem 2rem;
 text-align: center;
 margin: 3rem 0;
 position: relative;
}

.highlight-section h3 {
 font-size: 35px;
 margin-bottom: 1rem;
}

.highlight-section p {
 font-size: 26px;
}

.content-block {
 margin: 2rem 0;
}

.content-block ul {
 list-style-type: none;
 margin: 1rem 0;
 padding-left: 1.5rem;
}

.content-block ul li {
 margin-bottom: 0.5rem;
 position: relative;
}

.content-block ul li::before {
 content: "•";
 position: absolute;
 left: -1.5rem;
 color: var(--primary-color);
}

/* Responsive design */
@media (max-width: 1024px) {
 .title .text {
  font-size: 100px;
  line-height: 110px;
 }

 .container {
  padding: 0 1rem;
 }

 .image-split {
  flex-direction: column;
 }

 .split-image {
  width: 100%;
  height: 500px;
 }
}

@media (max-width: 768px) {
 .title .text {
  font-size: 70px;
  line-height: 80px;
 }

 .section {
  padding: 2rem 0;
 }

 .dark-banner h3 {
  font-size: 45px;
 }

 .large-text {
  font-size: 28px;
 }
}

@media (max-width: 480px) {
 .title .text {
  font-size: 50px;
  line-height: 60px;
 }

 .description {
  font-size: 16px;
 }

 .star-badge {
  width: 150px;
  height: 150px;
 }

 .star-content .percentage {
  font-size: 40px;
 }
}
