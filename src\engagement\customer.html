<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>The Journey</title>
  <link
   href="https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,700;1,400;1,700&family=Raleway:wght@400;600;700&display=swap"
   rel="stylesheet"
  />
  <link rel="stylesheet" href="customer.css" />
  <link rel="stylesheet" href="banner-fix.css" />
 </head>
 <body>
  <main>
   <!-- Initial Section -->
   <section class="initial-section">
    <div class="text-container">
     <p class="main-text">
      Genuine brand advocacy isn't just about repeat purchases, it's about
      creating reciprocal
      <span class="bold"
       >experiences that emotionally resonate with customers</span
      >
      at every stage of their journey.
     </p>
    </div>
   </section>

   <!-- Journey Cards Section -->
   <section class="journey-section">
    <div class="journey-text">
     <p>
      From the first interaction to becoming passionate brand ambassadors, the
      most successful brands invest in crafting
      <strong>meaningful environments, experiences, and emotions</strong>
      that foster long-term engagement and loyalty.
     </p>
     <p>
      Let's explore the key stages along the journey of transforming customers
      into advocates:
     </p>
    </div>

    <div class="journey-cards">
     <div class="journey-card">
      <div class="card-inner">
       <div class="card-front">
        <span class="card-number">(01)</span>
        <div class="card-title">
         <h3>rwfrefew</h3>
         <div class="card-line"></div>
        </div>
       </div>
       <div class="card-back">
        <span class="card-number">(01) Awareness</span>
        <div class="card-back-content">
         Capturing attention and making a strong first impression.
        </div>
       </div>
      </div>
     </div>
     <div class="journey-card">
      <div class="card-inner">
       <div class="card-front">
        <span class="card-number">(02)</span>
        <div class="card-title">
         <h3>Consideration</h3>
         <div class="card-line"></div>
        </div>
       </div>
       <div class="card-back">
        <span class="card-number">(02) Consideration</span>
        <div class="card-back-content">
         Building trust and demonstrating value through meaningful interactions.
        </div>
       </div>
      </div>
     </div>
     <div class="journey-card">
      <div class="card-inner">
       <div class="card-front">
        <span class="card-number">(03)</span>
        <div class="card-title">
         <h3>Enrolment</h3>
         <div class="card-line"></div>
        </div>
       </div>
       <div class="card-back">
        <span class="card-number">(03) Enrolment</span>
        <div class="card-back-content">
         Converting interest into meaningful commitment and first steps.
        </div>
       </div>
      </div>
     </div>
     <div class="journey-card">
      <div class="card-inner">
       <div class="card-front">
        <span class="card-number">(04)</span>
        <div class="card-title">
         <h3>Participation</h3>
         <div class="card-line"></div>
        </div>
       </div>
       <div class="card-back">
        <span class="card-number">(04) Participation</span>
        <div class="card-back-content">
         Fostering active engagement and meaningful interactions.
        </div>
       </div>
      </div>
     </div>
     <div class="journey-card">
      <div class="card-inner">
       <div class="card-front">
        <span class="card-number">(05)</span>
        <div class="card-title">
         <h3>Retention</h3>
         <div class="card-line"></div>
        </div>
       </div>
       <div class="card-back">
        <span class="card-number">(05) Retention</span>
        <div class="card-back-content">
         Nurturing lasting relationships through consistent value delivery.
        </div>
       </div>
      </div>
     </div>
     <div class="journey-card">
      <div class="card-inner">
       <div class="card-front">
        <span class="card-number">(06)</span>
        <div class="card-title">
         <h3>Advocacy</h3>
         <div class="card-line"></div>
        </div>
       </div>
       <div class="card-back">
        <span class="card-number">(06) Advocacy</span>
        <div class="card-back-content">
         Transforming satisfied customers into passionate brand ambassadors.
        </div>
       </div>
      </div>
     </div>
    </div>
    <div class="journey-conclusion">
     <p>
      Each stage plays a crucial role in engaging with casual customers and
      developing them into devoted advocates.
     </p>
     <p>
      Now, let's dive deeper into how brands can nurture customers along this
      journey.
     </p>
    </div>
   </section>

   <!-- Detailed Sections -->
   <div id="detailed-sections">
    <section class="advocacy-section">
     <div class="advocacy-card">
      <div class="advocacy-content">
       <h2>Brand Advocates</h2>
       <ul>
        <li>Deep emotional connection to your brand's values and mission</li>
        <li>Actively promote and recommend your brand to others</li>
        <li>Feel personally invested in your success</li>
        <li>Share testimonials, user-generated content, and referrals</li>
       </ul>
      </div>
     </div>
    </section>
   </div>

   <!-- Final Section -->
   <section class="final-section">
    <div class="final-card">
     <h2>
      So, how do brands go about <span class="bold">developing</span> and
      <span class="bold">nurturing brand advocates</span>?
     </h2>
    </div>
   </section>
  </main>
  <script src="customer.js"></script>
  <script>
   // Force initialization of banner slides after page load
   window.onload = function () {
    // Reinitialize banner slides
    const banners = document.querySelectorAll(
     ".early-access-banner, .blue-section"
    );

    banners.forEach((banner) => {
     const slides = banner.querySelectorAll(".slide");
     // Make first slide visible
     if (slides.length) {
      slides.forEach((slide) => {
       slide.style.opacity = "0";
       slide.style.visibility = "hidden";
       slide.classList.remove("active");
      });

      slides[0].style.opacity = "1";
      slides[0].style.visibility = "visible";
      slides[0].classList.add("active");
     }
    });
   };
  </script>
 </body>
</html>
