const customerSteps = [
 {
  id: "01",
  title: "Awareness",
  backContent: "Capturing attention and making a strong first impression.",
  content: `
            <p>To capture attention, a brand's messaging must be clear, relatable, and aligned<br> with its identity. Every detail, from visuals to tone of voice, should feel authentic<br> and consistent. Customers are more likely to engage with brands that resonate<br> with their values, feel transparent, and are approachable. If you can effectively<br> communicate how your products or services meet their needs, they'll be more<br> inclined to listen.</p>

            <p>Equally important is where and how customers first encounter a brand. Small details shape customer perceptions and influence whether they continue engaging. Whether online or in-store, those first environments and experiences must be seamless and intuitive. Today's consumers expect frictionless interactions:</p>

            <p><ul>
            <li><b>Online</b>: Slow load times, clunky checkouts, or technical glitches can quickly frustrate potential buyers and lead to abandoned carts.</li>

            <li><b>In-store:</b> How are customers welcomed? What customer service training have you provided employees? Is the layout intuitive? Does the space feel inviting?</li>
            </ul>
            <p>Awareness spans multiple channels and touchpoints, making a true omnichannel presence essential. But this goes beyond simply having an app, website, and physical store, it's about creating a connected experience that supports customers wherever they are and however they are wanting to interact.</p>

            <p>A modern shopper might browse on their phone during a morning commute, add an item to their cart on a desktop during a coffee break, visit a store to try it on, return home and complete the purchase, or decide to return it later. At every stage, they expect to be recognised and supported, not forced to start over. Brands that establish a consistent, unified presence early in the customer journey reinforce trust, authenticity, and reliability, increasing the likelihood of lasting engagement.</p>
        `,
  hasAccent: true,
  accentPosition: "right",
  accentImage: "../../public/39.png",
  color: "#d8bb93",
 },
 {
  id: "02",
  title: "Consideration",
  backContent:
   "Providing value and building trust to influence decision-making",
  content: `
            <h3 class="consideration-subtitle">Convince, intrigue and <em><b>excite</b></em></h3>
            
            <p class="consideration-intro">Once awareness is established, your brand becomes an option worth considering. But today's consumers are more discerning than ever, they research extensively, compare alternatives, read reviews, and carefully assess their choices before deciding.</p>

            <h3 class="consideration-question">So, how can your brand stand out and make a <em><b>lasting impression</b></em>?</h3>

            <p>Trust is the foundation of consideration. Consumers want transparency, authenticity, and alignment with their values. They care about a brand's ethics, business practices, and purpose. Clearly communicating who you are, what you stand for, and why your product matters help create an emotional connection.</p>

            <p>Use your platforms to:</p>
            <ul>
                <li>Share your brand story, values, and behind-the-scenes processes.</li>
                <li>Highlight ethical sourcing, sustainability efforts, or customer-centric initiatives.</li>
                <li>Reinforce a sense of fair exchange, customers want to feel good about where they spend their money.</li>
            </ul>

            <p>Beyond trust, intrigue and excitement play a crucial role in guiding customers toward emotional commitment. Make your brand relatable and engaging by creating content that speaks their language, authentic, accessible, and relevant. Showcase customer reviews and testimonials upfront to reinforce credibility. Go beyond functional benefits and highlight how your product enhances their lives, whether through convenience, security, joy, or confidence.</p>

            <p>If your brand offers a loyalty or membership programme, this is the moment to introduce its benefits. Show customers that choosing your brand isn't just a one-time decision, it's the start of an ongoing relationship. Make them excited about the exclusive rewards, perks, and recognition they'll receive by joining.</p>

            <p>The more personal, relevant, and emotionally engaging the experience, the more likely potential customers are to take the next step and commit to your brand.</p>
        `,
  images: ["../../public/pexels-cottonbro-********.png"],
  color: "#476477",
 },
 {
  id: "03",
  title: "Enrolment",
  backContent:
   "Making the transition from interest to action seamless and rewarding",
  content: `
            <h3 class="section-question">At this stage, customers are now ready to take action, whether that's signing up for a newsletter, creating an account, or making their first purchase. The key to converting interest into commitment is ensuring this transition is seamless, effortless, and rewarding.</h3>
            <p>A complicated or time-consuming sign-up process can deter potential customers. Whether online, in-app, or in-store, enrolment should be:</p>
            <ul>
                <li>Easy to navigate: Minimal steps, clear instructions, and user-friendly design</li>
                <li>Tailored to your audience: Consider different comfort levels with technology</li>
                <li>Flexible: Digital sign-ups are often faster, but some customers prefer in-person or assisted options</li>
            </ul>
            <p>A one-size-fits all approach doesn't work for enrolment. For brands with diverse customer bases, like supermarkets, phygital strategies (blending physical and digital experiences) can ensure a smooth, connected journey across all touchpoints.</p>
            <p>To reduce drop-off at this critical stage, offer meaningful incentives such as exclusive discounts or rewards that feel valuable rather than gimmicky, early access to premium content or perks that enhance brand exclusivity, and limited time offers that create urgency without pressure.</p>
            <p>However, transparency is key. Customers should be aware of incentives upfront rather than feeling they missed out on a better deal after enrolling. Subtle but strategic messaging at checkout or sign up can guide them without disrupting their journey.</p>
            <p>Enrolment isn't the finish line; it's the beginning of a relationship. First impressions still matter even after signing up. A well-crafted onboarding experience can reinforce their decision and build early engagement. This could be a personalised welcome email thanking customers for joining, or clearly signalled next step, introducing features, benefits, or community aspects.</p>
            <p>These small but thoughtful gestures establish a positive emotional connection, setting the stage for deeper engagement and long-term loyalty.</p>
        `,
  images: [
   "../../public/pexels-cottonbro-9168130.png",
   "../../public/pexels-cottonbro-9167936.png",
   "../../public/pexels-cottonbro-9168128.png",
   "../../public/pexels-cottonbro-9167952.png",
  ],
  color: "#81c6b4",
 },
 {
  id: "04",
  title: "Participation",
  subtitle: "Encouraging active engagement",
  backContent:
   "Creating opportunities for meaningful interaction and engagement",
  content: `
            <p>Once customers sign up, they enter an iterative cycle of participation and retention, two essential phases for long-term brand success. Participation is about actively engaging customers in ways that align with your brand, offering, and goals.</p>

            <p>Participation can take many forms, including:</p>
            <ul>
                <li><b>Repeat purchases</b> and consistent product or service use</li>
                <li><b>Social media interactions,</b> content engagement, and brand advocacy</li>
                <li><b>Attendance at events</b> or participation in brand-related experiences</li>
            </ul>

            <div class="section-question">
                <div class="key-text">The key?</div>
                <div class="main-text">Defining what meaningful engagement looks like for your brand, and fostering it through the right <em>environments</em>, <em>experiences</em>, and <em>emotions</em>.</div>
            </div>

            <p>While the core brand experience may remain the same pre and post enrolment, loyalty programmes can unlock exclusive environments for members, whether through an app, platform, or dedicated web space. Recognition and exclusivity are powerful motivators that deepen customer relationships.</p>

            <p>Loyal customers may stick with a brand out of habit or perceived value, but that doesn't mean they'll actively promote it. Traditional loyalty structures like points, levels, or badges can feel outdated when rewards are too slow to accumulate, unattainable or unclear, or lacking real value.</p>

            <p>Today's customers crave more. Recognition and exclusivity remain powerful motivators, but they must feel genuine and emotionally resonant.</p>

            <p>In a time when many people feel less socially connected, brands can create deeper loyalty by fostering a sense of belonging. Personalised invitations to real world experiences could be a compelling way to do this. For example, if a segment of your loyalty members frequently purchases yoga gear, invite them to an exclusive yoga morning in their city. Or if customers love horror books, offer them early access to a horror author reading.</p>

            <p>Even if they don't attend, the invitation alone makes them feel seen and valued, not just as a loyalty member, but as an individual. When customers feel uniquely understood, they are far more likely to engage with future brand interactions.</p>

            <p>The easier and more rewarding it is for customers to connect, engage, and be heard, the stronger their emotional connection to your brand will grow. The more touchpoints they interact with, the more likely they are to become brand advocates, organically driving growth and long-term success.</p>
        `,
  images: ["../../public/julian-myles-oiezmlcga6a-unsplash-1.png"],
  hasAccent: true,
  accentContent: {
   number: "-50%",
   text: "of active members",
  },
  starBadgePosition: "top",
  color: "#6b6f70",
 },
 {
  id: "05",
  title: "Retention",
  subtitle: "Keeping the <em><b>spark</b></em> alive",
  backContent:
   "Delivering consistent value to maintain long-term relationships",
  content: `
          
            <p class="retention-stats">Acquiring new customers is five to seven times more expensive than retaining existing ones. Loyal customers not only make repeat purchases, but they also spend more over time, potentially 10 times more than their initial purchase (Bia/Kelsey).</p>
            <p>Yet, loyalty is never a guarantee. As we've discussed, participation and retention go hand in hand, actively engaged customers are more likely to stay, but their needs, preferences, and expectations evolve over time. Brands must continually nurture this relationship to sustain long-term engagement.</p>
            <p>To sustain loyalty and keep customers engaged beyond initial sign up, brands must provide exceptional experiences at every touchpoint. The problem? Customers who joined your loyalty programme a year ago may not have the same preferences today. Their expectations shift, influenced by new trends, life changes, or external factors. To keep up, brands need flexible platforms that adapt to changing customer behaviours, agile loyalty mechanics that evolve alongside market trends, and personalized communication to maintain relevance and connection.</p>
            <p>By continuously refining environments, experiences, and emotional engagement, brands can build long lasting relationships that go beyond transactions.</p>
            <p>While discounts and promotions are still important to today's customer, they don't necessarily make them feel seen. True retention is about recognition and emotional connection. Instead of just rewarding purchases, consider:</p>
            <div class="early-access-banner" style="position: relative; overflow: visible; background-color: #8D8CC4;">
                <div class="banner-content" style="position: relative; overflow: visible; height: 380px;">
                    <div class="slide active" data-slide="1" style="position: absolute; opacity: 1; visibility: visible;">
                        <div class="slide-content" style="display: flex; flex-direction: column; align-items: center; text-align: center; width: 80%; padding-right: 80px;">
                            <h3 style="color: white; font-size: 35px; font-weight: 700; margin-bottom: 16px; display: block;">Early access</h3>
                            <p style="color: white; font-size: 26px; display: block;">to product launches or exclusive events.</p>
                        </div>
                    </div>
                    <div class="slide" data-slide="2" style="position: absolute; opacity: 0; visibility: hidden;">
                        <div class="slide-content" style="display: flex; flex-direction: column; align-items: center; text-align: center; width: 80%; padding-right: 80px;">
                            <h3 style="color: white; font-size: 35px; font-weight: 700; margin-bottom: 16px; display: block;">Personalised gifts</h3>
                            <p style="color: white; font-size: 26px; display: block;">that reflect individual preferences.</p>
                        </div>
                    </div>
                    <div class="slide" data-slide="3" style="position: absolute; opacity: 0; visibility: hidden;">
                        <div class="slide-content" style="display: flex; flex-direction: column; align-items: center; text-align: center; width: 80%; padding-right: 80px;">
                            <h3 style="color: white; font-size: 35px; font-weight: 700; margin-bottom: 16px; display: block;">VIP experiences</h3>
                            <p style="color: white; font-size: 26px; display: block;">based on engagement, not just spend.</p>
                        </div>
                    </div>
                    <div class="slide" data-slide="4" style="position: absolute; opacity: 0; visibility: hidden;">
                        <div class="slide-content" style="display: flex; flex-direction: column; align-items: center; text-align: center; width: 80%; padding-right: 80px;">
                            <h3 style="color: white; font-size: 35px; font-weight: 700; margin-bottom: 16px; display: block;">Recognition for participation</h3>
                            <p style="color: white; font-size: 26px; display: block;">whether it's social media engagement, event attendance, or advocacy. </p>
                        </div>
                    </div>
                    <button class="next-button" style="position: absolute; right: 20px; top: 50%; transform: translateY(-50%); z-index: 10; background: transparent; border: none; cursor: pointer;">
                        <img src="../../public/39-1.png" alt="Next arrow" width="60" height="60" style="filter: brightness(0) invert(1);" />
                    </button>
                </div>
            </div>
            <p>When customers feel valued beyond their wallet, they remain engaged, even when they're not actively purchasing.</p>
            <p>Showing customers that you see them as more than just a number will foster deeper, more meaningful relationships. Consistent reinforcements like this help build trust and make customers feel that their loyalty, no matter the form, is valued.</p>
            <p>Retention isn't just about product or service quality; it's about building those emotional connections. A strong relationship with your brand makes customers more forgiving of occasional hiccups, whether it's a delayed shipment or a less than perfect experience.</p>
            <p>But if recognition fades, and customers no longer feel appreciated, their loyalty will waver. The deeper the emotional connection, the stronger their commitment - even in uncertain times.</p>
            <p>By consistently providing exceptional environments and experiences, reinforcing positive emotions, and making customers feel truly valued, brands can build lasting loyalty that withstands challenges on both sides.</p>
        `,
  images: [
   "../../public/pexels-cottonbro-9155631.png",
   "../../public/pexels-cottonbro-9155636.png",
  ],
  hasAccent: true,
  accentContent: {
   number: "75%",
   text: "satisfaction",
  },
  color: "#f18f86",
 },
 {
  id: "06",
  title: "Advocacy",
  subtitle: "The difference between a Loyal Customer and a Brand Ambassador",
  backContent: "Empowering customers to become passionate brand ambassadors",
  content: `
            <p>The <b>Advocacy</b> stage is the most challenging, and rewarding, phase of the customer journey. Not every customer will reach this level, but those who do become your most valuable asset.</p>
            <p>So, what sets customer loyalty apart from brand advocacy?  Whilst loyal customers consistently buy from you, brand advocates take it a step further. They promote your brand organically, not because of rewards or incentives, but because they genuinely believe in what you stand for. </p>
        `,
  images: ["../../public/card0.png", "../../public/card1.png"],
  color: "#8d8cc4",
 },
];

function createDetailedSection(step) {
 const section = document.createElement("section");
 section.className = "detailed-section";

 // Set initial state (remove GSAP animation setup)
 section.style.opacity = 1;
 section.style.transform = "translateY(0)";

 if (step.fullWidthBanner) {
  section.style.width = "100%";
 }

 // Header section for all steps
 let html = `
        <div class="section-header">
            <span class="section-number">(${step.id})</span>
            <h2 class="section-title">${step.title}</h2>
            ${
             step.subtitle && step.id !== "04" && step.id !== "05"
              ? `<h3 class="section-subtitle">${step.subtitle}</h3>`
              : ""
            }
        </div>
    `;

 // Special handling for Participation section (04)
 if (step.id === "04") {
  // Create container for full-width image with overlay and badge
  html += `<div class="content-with-badge">`;

  // Add full-width image with subtitle overlay
  if (step.images && step.images.length > 0) {
   html += `
                <div class="images-grid" style="position: relative;">
                    ${step.images
                     .map(
                      (img) => `<img src="${img}" alt="${step.title} image">`
                     )
                     .join("")}
                    <h3 class="section-subtitle">Encouraging <em>active</em> engagement</h3>
                </div>
            `;
  }

  // Add star badge
  if (step.hasAccent && step.accentContent) {
   html += `
                <div class="star-badge">
                    <img src="../../public/star-1-1.svg" alt="Star background">
                    <div class="star-badge-content">
                        <div class="star-badge-number">${step.accentContent.number}</div>
                        <div class="star-badge-text">${step.accentContent.text}</div>
                    </div>
                </div>
            `;
  }

  // Close content-with-badge div
  html += `</div>`;

  // Add content sections
  const contentParts = step.content.split('<h3 class="section-question">');

  // First part of content
  html += `<div class="section-content">${contentParts[0]}</div>`;

  // "The key?" section and remaining content
  if (contentParts.length > 1) {
   html += `<div class="section-content"><h3 class="section-question">The key?<br>
            <span style="white-space: nowrap;">Defining what meaningful engagement looks</span><br>
            <span style="white-space: nowrap;">like for your brand, and fostering it through the right</span><br>
            <span style="white-space: nowrap;"><b>environments, experiences, and emotions.</b></span></h3>${contentParts[1].replace(
             "The key?<br>Defining what meaningful engagement looks<br>like for your brand, and fostering it through the right<br><b>environments, experiences, and emotions.</b></h3>",
             ""
            )}</div>`;
  }
 } else if (step.id === "05") {
  // Create container for full-width image with badge
  html += `<div class="content-with-badge">`;

  // Add full-width image without subtitle overlay
  if (step.images && step.images.length > 0) {
   html += `
                <div class="images-grid retention-images" style="position: relative;">
                    ${step.images
                     .map(
                      (img) => `<img src="${img}" alt="${step.title} image">`
                     )
                     .join("")}
                </div>
            `;
  }

  // Add the subtitle after the images
  html += `<h3 class="section-subtitle">Keeping the <em><b>spark</b></em> alive</h3>`;

  // Add single star badge with proper coloring
  if (step.hasAccent && step.accentContent) {
   html += `
                <div class="star-badge">
                    <img src="../../public/star-1.svg" alt="Star background">
                    <div class="star-badge-content">
                        <div class="star-badge-number">${step.accentContent.number}</div>
                        <div class="star-badge-text">${step.accentContent.text}</div>
                    </div>
                </div>
            `;
  }

  // Close content-with-badge div
  html += `</div>`;

  // Add content
  html += `<div class="section-content">${step.content}</div>`;
 } else if (step.id === "03") {
  // Special handling for Enrollment section (03)
  html += `
            <div class="section-content enrollment-section">
                <h3 class="enrollment-subtitle">Turning interest into <em><b>commitment</b></em></h3>
                <p>At this stage, customers are now ready to take action, whether that's signing up for a newsletter, creating an account, or making their first purchase. The key to converting interest into commitment is ensuring this transition is seamless, effortless, and rewarding.</p>
                <p>A complicated or time-consuming sign-up process can deter potential customers. Whether online, in-app, or in-store, enrolment should be:</p>
                <ul>
                    <li><strong>Easy to navigate:</strong> Minimal steps, clear instructions, and user-friendly design.</li>
                    <li><strong>Tailored to your audience:</strong> Consider different comfort levels with technology.</li>
                    <li><strong>Flexible:</strong> Digital sign-ups are often faster, but some customers prefer in-person or assisted options.</li>
                </ul>
                <p>A one-size-fits all approach doesn't work for enrolment. For brands with diverse customer bases, like supermarkets, phygital strategies (blending physical and digital experiences) can ensure a smooth, connected journey across all touchpoints.</p>
                <p>To reduce drop-off at this critical stage, offer meaningful incentives such as exclusive discounts or rewards that feel valuable rather than gimmicky, early access to premium content or perks that enhance brand exclusivity, and limited time offers that create urgency without pressure.</p>
                <p>However, transparency is key. Customers should be aware of incentives upfront rather than feeling they missed out on a better deal after enrolling. Subtle but strategic messaging at checkout or sign up can guide them without disrupting their journey.</p>
                <p>Enrolment isn't the finish line; it's the beginning of a relationship. First impressions still matter even after signing up. A well-crafted onboarding experience can reinforce their decision and build early engagement. This could be a personalised welcome email thanking customers for joining, or clearly signalled next step, introducing features, benefits, or community aspects.</p>
                <p>These small but thoughtful gestures establish a positive emotional connection, setting the stage for deeper engagement and long-term loyalty.</p>
            </div>
        `;

  // Add enrollment images in a grid
  if (step.images && step.images.length > 0) {
   html += `
                <div class="enrollment-images-grid">
                    ${step.images
                     .map(
                      (img) => `<img src="${img}" alt="${step.title} image">`
                     )
                     .join("")}
                </div>
            `;
  }
 } else if (step.id === "02") {
  // Special handling for Consideration section (02) - similar to layout shown in the image
  html += `<div class="consideration-layout">`;

  // Text content on the left
  html += `
            <div class="consideration-text">
                <h3 class="consideration-subtitle">Convince, intrigue and <em><b>excite</b></em></h3>
                <p class="consideration-intro">Once awareness is established, your brand becomes an option worth considering. But today's consumers are more discerning than ever, they research extensively, compare alternatives, read reviews, and carefully assess their choices before deciding.</p>
            </div>
        `;

  // Add image on the right
  if (step.images && step.images.length > 0) {
   html += `
                <div class="consideration-image-container">
                    <img src="${step.images[0]}" alt="${step.title} image" class="consideration-image">
                </div>
            `;
  }

  // Close consideration layout div
  html += `</div>`;

  // Add remaining content
  html += `
            <div class="section-content">
                <h3 class="consideration-question">So, how can your brand stand out and make a <em><b>lasting impression</b></em>?</h3>
                <p>Trust is the foundation of consideration. Consumers want transparency, authenticity, and alignment with their values. They care about a brand's ethics, business practices, and purpose. Clearly communicating who you are, what you stand for, and why your product matters help create an emotional connection.</p>
                <p>Use your platforms to:</p>
                <ul>
                    <li>Share your brand story, values, and behind-the-scenes processes.</li>
                    <li>Highlight ethical sourcing, sustainability efforts, or customer-centric initiatives.</li>
                    <li>Reinforce a sense of fair exchange, customers want to feel good about where they spend their money.</li>
                </ul>
                <p>Beyond trust, intrigue and excitement play a crucial role in guiding customers toward emotional commitment. Make your brand relatable and engaging by creating content that speaks their language, authentic, accessible, and relevant. Showcase customer reviews and testimonials upfront to reinforce credibility. Go beyond functional benefits and highlight how your product enhances their lives, whether through convenience, security, joy, or confidence.</p>
                <p>If your brand offers a loyalty or membership programme, this is the moment to introduce its benefits. Show customers that choosing your brand isn't just a one-time decision, it's the start of an ongoing relationship. Make them excited about the exclusive rewards, perks, and recognition they'll receive by joining.</p>
                <p>The more personal, relevant, and emotionally engaging the experience, the more likely potential customers are to take the next step and commit to your brand.</p>
            </div>
        `;
 } else if (step.id === "01") {
  // Special handling for Awareness section (01)
  html += `<div class="section-content">${step.content}</div>`;

  // Add accent box for sections that need it
  if (step.hasAccent && step.accentImage) {
   html += `
                <div class="accent-box">
                    <img src="../../public/39.png" alt="Toggle content" class="left-arrow">
                    <img src="../../public/39-1.png" alt="Toggle content" class="right-arrow">
                    <div class="expanded-content">
                        <h3>Making first <i><b>impressions</b></i> that last</h3>
                        <p>The first step on the journey is <b>Awareness</b>. The first moment a potential customer discovers a brand or product. But it's about much more than just being eye catching, it's about creating an immediate, meaningful connection before a purchase is even considered. And as we all know, first impressions matter.</p>
                    </div>
                </div>
            `;
  }
 } else if (step.id === "06") {
  // Special handling for Advocacy section (06)
  html += `
            <div class="section-content advocacy-section">
                <div class="section-header-advocacy">
                    <span class="section-number">(${step.id})</span>
                    <h2 class="section-title">${step.title}</h2>
                </div>
                <h3 class="advocacy-subtitle">The difference between a Loyal Customer and a <b><em>Brand Ambassador</em></b></h3>
                <p>The <b>Advocacy</b> stage is the most challenging, and rewarding, phase of the customer journey. Not every customer will reach this level, but those who do become your most valuable asset.</p>
                <p>So, what sets customer loyalty apart from brand advocacy? Whilst loyal customers consistently buy from you, brand advocates take it a step further. They promote your brand organically, not because of rewards or incentives, but because they genuinely believe in what you stand for.</p>
            </div>

            <!-- Horizontal scrolling images container -->
            <div class="scrolling-images-container">
                <div class="scrolling-images-wrapper">
                    <img src="${step.images[0]}" alt="Advocacy image 1" class="scroll-image">
                    <img src="${step.images[1]}" alt="Advocacy image 2" class="scroll-image">
                </div>
            </div>
        `;
 } else {
  // Standard content for other sections
  html += `<div class="section-content">${step.content}</div>`;

  // Add star badge for sections with top positioning (except 04)
  if (
   step.hasAccent &&
   step.accentContent &&
   step.starBadgePosition === "top" &&
   step.id !== "04"
  ) {
   html += `
                <div class="star-badge">
                    <img src="../../public/star-1.svg" alt="Star background">
                    <div class="star-badge-content">
                        <div class="star-badge-number">${step.accentContent.number}</div>
                        <div class="star-badge-text">${step.accentContent.text}</div>
                    </div>
                </div>
            `;
  }

  // Add images for other sections
  if (step.images && step.images.length > 0) {
   // Standard image grid for other sections
   const gridClass =
    step.images.length <= 2 ? "images-grid images-grid-2" : "images-grid";
   html += `
                <div class="${gridClass}">
                    ${step.images
                     .map(
                      (img) => `<img src="${img}" alt="${step.title} image">`
                     )
                     .join("")}
                </div>
            `;
  }
 }

 // Add accent box for sections that need it (moved this outside of the else if for section 01)
 if (step.hasAccent && step.accentImage && step.id !== "01") {
  html += `
            <div class="accent-box">
                <img src="../../public/39.png" alt="Toggle content" class="left-arrow">
                <img src="../../public/39-1.png" alt="Toggle content" class="right-arrow">
                <div class="expanded-content">
                    <h3>Making first impressions that last</h3>
                    <p>The first step on the journey is Awareness. The first moment a potential customer discovers a brand or product. But it's about much more than just being eye catching, it's about creating an immediate, meaningful connection before a purchase is even considered. And as we all know, first impressions matter.</p>
                </div>
            </div>
        `;
 }

 // Add star badge for sections that don't have top positioning (except 04 and 05)
 if (
  step.hasAccent &&
  step.accentContent &&
  step.starBadgePosition !== "top" &&
  step.id !== "04" &&
  step.id !== "05"
 ) {
  html += `
            <div class="star-badge">
                <img src="../../public/star-1.svg" alt="Star background">
                <div class="star-badge-content">
                    <div class="star-badge-number">${step.accentContent.number}</div>
                    <div class="star-badge-text">${step.accentContent.text}</div>
                </div>
            </div>
        `;
 }

 section.innerHTML = html;
 section.style.backgroundColor = step.backgroundColor || "#FFFFFF";

 // Remove the IntersectionObserver for GSAP animations

 // Handle touch events with improved mobile support
 const touchState = {
  startY: 0,
  startX: 0,
  isScrolling: false,
  timeout: null,
 };

 const handleTouchStart = (e) => {
  touchState.startY = e.touches[0].clientY;
  touchState.startX = e.touches[0].clientX;
  touchState.isScrolling = false;
 };

 const handleTouchMove = (e) => {
  if (touchState.isScrolling) return;

  const touchY = e.touches[0].clientY;
  const touchX = e.touches[0].clientX;
  const deltaY = touchState.startY - touchY;
  const deltaX = touchState.startX - touchX;

  // Determine if scroll is more horizontal or vertical
  if (Math.abs(deltaX) > Math.abs(deltaY)) {
   // Horizontal scroll handling
   if (Math.abs(deltaX) > 50) {
    touchState.isScrolling = true;
    // Handle horizontal scroll actions
   }
  } else {
   // Vertical scroll handling
   if (Math.abs(deltaY) > 50) {
    touchState.isScrolling = true;
    // Handle vertical scroll actions
   }
  }
 };

 const handleTouchEnd = () => {
  if (touchState.timeout) {
   clearTimeout(touchState.timeout);
  }
  touchState.timeout = setTimeout(() => {
   touchState.isScrolling = false;
  }, 100);
 };

 // Add touch event listeners
 section.addEventListener("touchstart", handleTouchStart, { passive: true });
 section.addEventListener("touchmove", handleTouchMove, { passive: true });
 section.addEventListener("touchend", handleTouchEnd, { passive: true });

 // Add click handler for star badge if it exists
 const starBadge = section.querySelector(".star-badge");
 if (starBadge) {
  starBadge.addEventListener("click", () => {
   const isParticipation = step.id === "04";
   const isRetention = step.id === "05";
   showPopup(
    step.accentContent.number,
    isParticipation
     ? "While the number of loyalty programmes per person continues to increase, active participation remains below 50% of members.(BCG)"
     : "of member satisfaction with loyalty programmes is shaped by their experience with the programme and brand, as well as the rules and rewards it offers.(Merkel)",
    isRetention ? "purple" : "blue"
   );
  });
 }

 // Add click handler for accent box if it exists
 const accentBox = section.querySelector(".accent-box");
 if (accentBox) {
  accentBox.addEventListener("click", () => {
   accentBox.classList.toggle("active");

   // If it's not yet visible (part of initial animation), make it visible when clicked
   if (!accentBox.classList.contains("visible")) {
    accentBox.classList.add("visible");
   }

   // Ensure the expanded content elements are visible when active
   const expandedContent = accentBox.querySelector(".expanded-content");
   if (expandedContent) {
    if (accentBox.classList.contains("active")) {
     expandedContent.style.opacity = "1";
    } else {
     expandedContent.style.opacity = "0";
    }
   }
  });
 }

 // Add responsive styles for small screens
 const style = document.createElement("style");
 style.textContent = `
        @media (max-width: 405px) {
            .section-content h2,
            .section-content h3 {
                font-size: 1.25rem;
                line-height: 1.4;
                word-wrap: break-word;
                white-space: normal;
            }

            .section-content p {
                font-size: 0.875rem;
                line-height: 1.5;
            }

            .section-title {
                font-size: 1.25rem;
                white-space: normal;
                word-wrap: break-word;
            }

            .advocacy-subtitle,
            .section-subtitle {
                font-size: 1rem;
                line-height: 1.4;
                white-space: normal;
                word-wrap: break-word;
            }
        }
    `;
 document.head.appendChild(style);

 // Set up animations for the section using slide-in animations for all content
 // 1. Apply to text elements
 const textElements = section.querySelectorAll(
  "p, h1, h2, h3, h4, h5, h6, li, .card-title, .section-title, .section-subtitle, .main-text, .card-number, .card-back-content"
 );

 textElements.forEach((element, index) => {
  // Skip elements that already have animations
  if (
   !element.classList.contains("word") &&
   !element.parentElement.classList.contains("word")
  ) {
   element.classList.add("animationText");

   // Add delay classes in a pattern for staggered animation
   const delayClass = `delay-${(index % 5) + 1}`;
   element.classList.add(delayClass);
  }
 });

 // 2. Apply to image elements
 const imageElements = section.querySelectorAll(
  'img:not([src*="star-1"]), .images-grid, .consideration-image-container, .enrollment-images-grid, .scrolling-images-container'
 );

 imageElements.forEach((element, index) => {
  element.classList.add("slide-in-image");

  // Add delay classes in a pattern for staggered animation
  const delayClass = `delay-${(index % 5) + 1}`;
  element.classList.add(delayClass);
 });

 // 3. Apply to other content elements
 const contentElements = section.querySelectorAll(
  ".section-content, .section-header, .card-inner, .accent-box, .star-badge, .content-with-badge, .early-access-banner, .banner-content"
 );

 contentElements.forEach((element, index) => {
  element.classList.add("slide-in-element");

  // Add delay classes in a pattern for staggered animation
  const delayClass = `delay-${(index % 5) + 1}`;
  element.classList.add(delayClass);
 });

 // Create Intersection Observer for this section's elements
 const observer = new IntersectionObserver(
  (entries) => {
   entries.forEach((entry) => {
    if (entry.isIntersecting) {
     entry.target.classList.add("visible");
     // Unobserve after animation to improve performance
     observer.unobserve(entry.target);
    }
   });
  },
  {
   root: null,
   threshold: 0.1,
   rootMargin: "0px 0px -50px 0px",
  }
 );

 // Observe all elements with slide-in classes in this section
 section
  .querySelectorAll(".animationText, .slide-in-image, .slide-in-element")
  .forEach((element) => {
   observer.observe(element);
  });

 return section;
}

function showPopup(number, text, colorClass) {
 // Create overlay if it doesn't exist
 let overlay = document.querySelector(".popup-overlay");
 if (!overlay) {
  overlay = document.createElement("div");
  overlay.className = "popup-overlay";
  document.body.appendChild(overlay);
 }

 // Create popup if it doesn't exist
 let popup = document.querySelector(".star-badge-popup");
 if (!popup) {
  popup = document.createElement("div");
  popup.className = "star-badge-popup";
  document.body.appendChild(popup);
 }

 // Set popup content with explicit color class
 popup.innerHTML = `
        <img src="../public/close-icon.svg" alt="Close" class="popup-close">
        <div class="popup-number ${colorClass}">${number}</div>
        <div class="popup-text">${text}</div>
    `;

 // Add mobile-friendly class for small screens
 if (window.innerWidth <= 500) {
  popup.classList.add("mobile-friendly");
 } else {
  popup.classList.remove("mobile-friendly");
 }

 // Show overlay and popup
 overlay.classList.add("active");
 popup.classList.add("active");

 // Add close handlers
 const closeBtn = popup.querySelector(".popup-close");
 const closePopup = () => {
  overlay.classList.remove("active");
  popup.classList.remove("active");
 };

 closeBtn.addEventListener("click", closePopup);
 overlay.addEventListener("click", closePopup);

 // Handle window resize to adjust mobile class
 const handleResize = () => {
  if (window.innerWidth <= 500) {
   popup.classList.add("mobile-friendly");
  } else {
   popup.classList.remove("mobile-friendly");
  }
 };

 window.addEventListener("resize", handleResize);

 // Remove resize listener when popup is closed
 closeBtn.addEventListener("click", () => {
  window.removeEventListener("resize", handleResize);
 });
 overlay.addEventListener("click", () => {
  window.removeEventListener("resize", handleResize);
 });
}

// Add this near the top of the file with other initialization code
let horizontalScrollComplete = false;
let secondCardRevealed = false;

// Add this function to handle horizontal scroll behavior
function initializeHorizontalScroll() {
 const container = document.querySelector(".horizontal-cards-container");
 if (!container) return; // Early return if container doesn't exist

 const scroller = container.querySelector(".horizontal-cards-scroller");
 if (!scroller) return; // Early return if scroller doesn't exist

 const secondCard = container.querySelector(".horizontal-card:nth-child(2)");
 if (!secondCard) return; // Early return if second card doesn't exist

 let startX;
 let isScrolling = false;
 let scrollTimeout;

 function enableVerticalScroll() {
  horizontalScrollComplete = true;
  container.style.overflowY = "auto";
  document.body.style.overflowY = "auto";
 }

 function handleWheel(e) {
  // If we're not in the customer section anymore, remove this handler and restore normal scrolling
  if (
   !document.querySelector(".journey-section") ||
   !document.querySelector(".journey-section").closest("body")
  ) {
   document.removeEventListener("wheel", handleWheel, { passive: false });
   window.removeEventListener("wheel", handleWheel, { passive: false });
   document.body.style.overflow = "auto";
   document.documentElement.style.overflow = "auto";
   return;
  }

  if (!horizontalScrollComplete) {
   e.preventDefault(); // Always prevent default while horizontal scroll isn't complete

   if (!secondCardRevealed) {
    // Clear any existing timeout
    if (scrollTimeout) {
     clearTimeout(scrollTimeout);
    }

    if (e.deltaX > 0 || e.deltaY > 0) {
     scroller.style.transform = `translateX(-100vw)`;
     secondCard.classList.add("revealed");
     secondCardRevealed = true;

     // Set a new timeout for enabling vertical scroll
     scrollTimeout = setTimeout(enableVerticalScroll, 800);
    }
   }
  }
 }

 // Handle mouse wheel events
 container.addEventListener("wheel", handleWheel, { passive: false });

 // Handle touch events for mobile
 container.addEventListener(
  "touchstart",
  (e) => {
   if (!horizontalScrollComplete) {
    startX = e.touches[0].pageX - container.offsetLeft;
    isScrolling = true;
   }
  },
  { passive: true }
 );

 container.addEventListener(
  "touchmove",
  (e) => {
   if (!isScrolling || horizontalScrollComplete) return;

   e.preventDefault();
   const x = e.touches[0].pageX - container.offsetLeft;
   const walk = (x - startX) * 2;

   if (walk < -50 && !secondCardRevealed) {
    scroller.style.transform = `translateX(-100vw)`;
    secondCard.classList.add("revealed");
    secondCardRevealed = true;

    // Clear any existing timeout
    if (scrollTimeout) {
     clearTimeout(scrollTimeout);
    }

    // Set new timeout for enabling vertical scroll
    scrollTimeout = setTimeout(enableVerticalScroll, 800);
   }
  },
  { passive: false }
 );

 container.addEventListener(
  "touchend",
  () => {
   isScrolling = false;
  },
  { passive: true }
 );
}

document.addEventListener("DOMContentLoaded", () => {
 const detailedSections = document.getElementById("detailed-sections");
 customerSteps.forEach((step) => {
  detailedSections.appendChild(createDetailedSection(step));
 });

 // Initialize horizontal scrolling for advocacy images
 const initializeImageScroll = () => {
  const scrollContainer = document.querySelector(".scrolling-images-container");
  const scrollWrapper = document.querySelector(".scrolling-images-wrapper");
  const images = scrollWrapper
   ? scrollWrapper.querySelectorAll(".scroll-image")
   : [];
  let currentImageIndex = 0;
  let isScrolling = false;

  // Set overflow hidden explicitly
  if (scrollContainer) {
   scrollContainer.style.overflow = "hidden";
  }

  const showImage = (index) => {
   images.forEach((img) => {
    img.classList.remove("visible");
   });
   images[index].classList.add("visible");
   scrollWrapper.style.transform = `translateX(-${index * 50}%)`;
  };

  const handleScroll = (e) => {
   if (isScrolling) return;

   // Prevent default scroll behavior
   e.preventDefault();

   const deltaY = e.deltaY || (e.touches && e.touches[0].clientY - startY);

   // Determine scroll direction
   if (Math.abs(deltaY) > 50) {
    isScrolling = true;

    if (deltaY < 0) {
     // Scrolling up - show previous image
     if (currentImageIndex > 0) {
      currentImageIndex--;
      showImage(currentImageIndex);
     }
    } else {
     // Scrolling down - show next image
     if (currentImageIndex < images.length - 1) {
      currentImageIndex++;
      showImage(currentImageIndex);
     }
    }

    // Reset scrolling flag after animation completes
    setTimeout(() => {
     isScrolling = false;

     // Enable vertical scroll if we're at either end
     if (currentImageIndex === 0 || currentImageIndex === images.length - 1) {
      scrollContainer.style.overflowY = "auto";
      document.body.style.overflowY = "auto";
     }
    }, 500);
   }
  };

  if (scrollContainer && scrollWrapper && images.length > 0) {
   // Handle mouse wheel
   scrollContainer.addEventListener("wheel", handleScroll, { passive: false });

   // Handle touch events
   let startY;
   let startX;

   scrollContainer.addEventListener(
    "touchstart",
    (e) => {
     startY = e.touches[0].clientY;
     startX = e.touches[0].clientX;
    },
    { passive: true }
   );

   scrollContainer.addEventListener(
    "touchmove",
    (e) => {
     if (!isScrolling) {
      const currentY = e.touches[0].clientY;
      const deltaY = startY - currentY;
      handleScroll({ preventDefault: () => {}, deltaY });
     }
    },
    { passive: false }
   );

   // Show first image initially
   showImage(0);
  }
 };

 // Initialize image scroll
 initializeImageScroll();

 // Initialize horizontal cards
 const initializeHorizontalCards = () => {
  const advocacySection = document.querySelector(
   ".detailed-section:nth-child(6)"
  );
  if (!advocacySection) return;

  const cardContainer = advocacySection.querySelector(
   ".horizontal-cards-container"
  );
  if (!cardContainer) return;

  const scroller = cardContainer.querySelector(".horizontal-cards-scroller");
  const cards = scroller.querySelectorAll(".horizontal-card");

  if (!scroller || cards.length < 2) return;

  // Function to check if element is in viewport
  const isInViewport = (element, offset = 0) => {
   const rect = element.getBoundingClientRect();
   return rect.top <= window.innerHeight - offset && rect.bottom >= 0;
  };

  // Function to handle scroll
  const handleScroll = () => {
   if (isInViewport(cardContainer, window.innerHeight * 0.4)) {
    cards[1].classList.add("revealed");
    scroller.style.transform = "translateX(-590px)"; // Card width (560px) + gap (30px)
   } else {
    cards[1].classList.remove("revealed");
    scroller.style.transform = "translateX(0)";
   }
  };

  // Initial check
  handleScroll();

  // Add scroll event listener
  window.addEventListener("scroll", handleScroll, { passive: true });
 };

 // Initialize cards
 initializeHorizontalCards();

 // Update journey cards with flip functionality
 const journeyCards = document.querySelectorAll(".journey-card");
 journeyCards.forEach((card, index) => {
  const step = customerSteps[index];

  // Create inner structure for flip effect
  const cardInner = document.createElement("div");
  cardInner.className = "card-inner";
  cardInner.style.backgroundColor = step.color;

  // Front of card
  const cardFront = document.createElement("div");
  cardFront.className = "card-front";
  cardFront.innerHTML = `
            <span class="card-number">(${step.id})</span>
            <div class="card-title">
                <div class="card-line"></div>
                <h3>${step.title}</h3>
            </div>
        `;

  // Back of card
  const cardBack = document.createElement("div");
  cardBack.className = "card-back";
  cardBack.innerHTML = `
            <span class="card-number">(${step.id}) ${step.title}</span>
            <div class="card-back-content">${step.backContent}</div>
        `;

  // Assemble card
  cardInner.appendChild(cardFront);
  cardInner.appendChild(cardBack);

  // Clear existing content and add new structure
  card.innerHTML = "";
  card.appendChild(cardInner);

  // Add hover handlers for flip effect
  card.addEventListener("mouseenter", () => {
   card.classList.add("flipped");
  });

  card.addEventListener("mouseleave", () => {
   card.classList.remove("flipped");
  });

  // Add touch support for mobile devices
  let touchTimeout;
  card.addEventListener(
   "touchstart",
   (e) => {
    e.preventDefault(); // Prevent mouse events from firing
    card.classList.add("flipped");

    // Auto-flip back after 3 seconds on touch
    if (touchTimeout) clearTimeout(touchTimeout);
    touchTimeout = setTimeout(() => {
     card.classList.remove("flipped");
    }, 3000);
   },
   { passive: false }
  );

  // Optional: Allow manual touch end to flip back
  card.addEventListener("touchend", () => {
   // Keep the card flipped for the timeout duration
   // User can touch another card to flip this one back
  });
 });

 // Add banner slide functionality
 function setupBannerSlides() {
  const banners = document.querySelectorAll(
   ".early-access-banner, .blue-section"
  );

  banners.forEach((banner) => {
   const slides = banner.querySelectorAll(".slide");
   const nextButton = banner.querySelector(".next-button");
   let currentSlide = 0;

   if (!slides.length || !nextButton) return;

   const showSlide = (index) => {
    // Hide all slides first
    slides.forEach((slide) => {
     slide.classList.remove("active");
     slide.classList.remove("previous");
     slide.style.opacity = "0";
     slide.style.visibility = "hidden";
    });

    // Show current slide
    slides[index].classList.add("active");
    slides[index].style.opacity = "1";
    slides[index].style.visibility = "visible";
   };

   const nextSlide = () => {
    currentSlide = (currentSlide + 1) % slides.length;
    showSlide(currentSlide);
   };

   nextButton.addEventListener("click", nextSlide);

   // Auto-advance slides every 7 seconds
   let slideInterval = setInterval(nextSlide, 7000);

   // Pause auto-advance on hover
   banner.addEventListener("mouseenter", () => clearInterval(slideInterval));
   banner.addEventListener("mouseleave", () => {
    slideInterval = setInterval(nextSlide, 7000);
   });

   // Force visibility of first slide on page load
   setTimeout(() => {
    showSlide(0);
   }, 500);
  });
 }

 // Initialize banner slides
 setupBannerSlides();

 // Initialize horizontal scroll and set initial overflow states
 document.body.style.overflowY = "hidden";
 const container = document.querySelector(".horizontal-cards-container");
 if (container) {
  container.style.overflowY = "hidden";
 }
 initializeHorizontalScroll();
});
